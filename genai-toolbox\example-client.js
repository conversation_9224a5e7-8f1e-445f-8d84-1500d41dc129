// GenAI Toolbox 客户端示例
// 演示如何通过HTTP API与MCP服务器交互

class ToolboxClient {
    constructor(baseUrl = 'http://localhost:5000') {
        this.baseUrl = baseUrl;
    }

    // 检查服务器健康状态
    async checkHealth() {
        try {
            const response = await fetch(`${this.baseUrl}/health`);
            return await response.json();
        } catch (error) {
            console.error('健康检查失败:', error);
            return null;
        }
    }

    // 获取所有可用工具
    async getTools() {
        try {
            const response = await fetch(`${this.baseUrl}/tools`);
            return await response.json();
        } catch (error) {
            console.error('获取工具列表失败:', error);
            return null;
        }
    }

    // 获取工具集
    async getToolset(toolsetName = '') {
        try {
            const url = toolsetName 
                ? `${this.baseUrl}/toolsets/${toolsetName}`
                : `${this.baseUrl}/toolsets`;
            const response = await fetch(url);
            return await response.json();
        } catch (error) {
            console.error('获取工具集失败:', error);
            return null;
        }
    }

    // 调用工具
    async invokeTool(toolName, parameters = {}) {
        try {
            const response = await fetch(`${this.baseUrl}/tools/${toolName}/invoke`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(parameters)
            });
            return await response.json();
        } catch (error) {
            console.error(`调用工具 ${toolName} 失败:`, error);
            return null;
        }
    }
}

// 使用示例
async function demonstrateToolbox() {
    console.log('=== GenAI Toolbox 客户端演示 ===');
    
    const client = new ToolboxClient();

    // 1. 检查服务器状态
    console.log('\n1. 检查服务器健康状态...');
    const health = await client.checkHealth();
    if (health) {
        console.log('✅ 服务器运行正常:', health);
    } else {
        console.log('❌ 服务器无法访问');
        return;
    }

    // 2. 获取可用工具
    console.log('\n2. 获取可用工具...');
    const tools = await client.getTools();
    if (tools) {
        console.log('✅ 可用工具:', tools.map(t => t.name));
    }

    // 3. 获取工具集
    console.log('\n3. 获取工具集...');
    const toolsets = await client.getToolset();
    if (toolsets) {
        console.log('✅ 可用工具集:', Object.keys(toolsets));
    }

    // 4. 调用示例工具
    console.log('\n4. 调用示例工具...');
    
    // 创建表
    const createResult = await client.invokeTool('create-users-table');
    if (createResult) {
        console.log('✅ 创建用户表:', createResult);
    }

    // 插入用户
    const insertResult = await client.invokeTool('insert-user', {
        name: '张三',
        email: '<EMAIL>'
    });
    if (insertResult) {
        console.log('✅ 插入用户:', insertResult);
    }

    // 查询用户
    const usersResult = await client.invokeTool('get-users');
    if (usersResult) {
        console.log('✅ 查询用户:', usersResult);
    }

    console.log('\n=== 演示完成 ===');
}

// 在Node.js环境中运行
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { ToolboxClient, demonstrateToolbox };
    
    // 如果直接运行此文件
    if (require.main === module) {
        demonstrateToolbox().catch(console.error);
    }
}

// 在浏览器环境中运行
if (typeof window !== 'undefined') {
    window.ToolboxClient = ToolboxClient;
    window.demonstrateToolbox = demonstrateToolbox;
}
