# GenAI Toolbox 与 Augment Code 集成指南

## 概述

GenAI Toolbox MCP服务器现已成功安装在您的Windows环境中。本指南将说明如何将其与Augment Code和其他AI开发工具集成使用。

## 安装状态

✅ **安装完成**
- 可执行文件：`C:\Users\<USER>\go\bin\genai-toolbox.exe`
- 版本：v0.10.0
- 配置文件：`e:\Html\genai-toolbox\tools.yaml`

## 基本使用

### 1. 启动MCP服务器

```powershell
# 方法1：直接启动
cd e:\Html\genai-toolbox
C:\Users\<USER>\go\bin\genai-toolbox.exe --tools-file tools.yaml

# 方法2：使用启动脚本
.\start-toolbox.ps1
```

服务器将在 `http://localhost:5000` 上运行

### 2. 验证服务器运行

打开浏览器访问：`http://localhost:5000/health`

## 与AI工具集成

### 1. HTTP API集成

GenAI Toolbox提供RESTful API，可以通过HTTP请求调用：

```javascript
// 示例：获取所有工具
fetch('http://localhost:5000/tools')
  .then(response => response.json())
  .then(data => console.log(data));

// 示例：调用工具
fetch('http://localhost:5000/tools/list-tables/invoke', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({})
})
.then(response => response.json())
.then(data => console.log(data));
```

### 2. MCP协议集成

对于支持MCP协议的AI工具，可以直接连接：

```json
{
  "mcpServers": {
    "genai-toolbox": {
      "command": "C:\\Users\\<USER>\\go\\bin\\genai-toolbox.exe",
      "args": ["--stdio", "--tools-file", "e:\\Html\\genai-toolbox\\tools.yaml"]
    }
  }
}
```

### 3. 在Augment Code中使用

1. **作为后端服务**：启动GenAI Toolbox作为后端服务
2. **API调用**：在AI代理中通过HTTP API调用数据库功能
3. **上下文增强**：为AI助手提供实时数据库查询能力

## 配置数据库连接

### SQLite（推荐用于测试）

```yaml
sources:
  my-sqlite:
    kind: sqlite
    database: "./my-database.db"
```

### PostgreSQL

```yaml
sources:
  my-postgres:
    kind: postgres
    host: localhost
    port: 5432
    database: mydb
    user: myuser
    password: mypassword
```

### MySQL

```yaml
sources:
  my-mysql:
    kind: mysql
    host: localhost
    port: 3306
    database: mydb
    user: myuser
    password: mypassword
```

## 示例工具定义

```yaml
tools:
  get-users:
    kind: postgres-sql
    source: my-postgres
    description: "获取所有用户"
    statement: "SELECT * FROM users;"
    
  search-users:
    kind: postgres-sql
    source: my-postgres
    description: "搜索用户"
    parameters:
      - name: name
        type: string
        description: "用户名"
    statement: "SELECT * FROM users WHERE name LIKE '%' || $1 || '%';"
```

## 最佳实践

1. **安全性**：在生产环境中使用环境变量存储数据库凭据
2. **性能**：利用连接池和缓存机制
3. **监控**：启用OpenTelemetry监控
4. **版本控制**：将配置文件纳入版本控制

## 故障排除

### 常见问题

1. **服务器无法启动**
   - 检查配置文件语法
   - 确认数据库连接信息正确
   - 查看日志输出

2. **数据库连接失败**
   - 验证数据库服务是否运行
   - 检查网络连接
   - 确认认证信息

3. **工具调用失败**
   - 检查SQL语句语法
   - 验证参数类型和数量
   - 查看错误日志

### 调试命令

```powershell
# 启用调试日志
C:\Users\<USER>\go\bin\genai-toolbox.exe --tools-file tools.yaml --log-level DEBUG

# 检查配置
C:\Users\<USER>\go\bin\genai-toolbox.exe --tools-file tools.yaml --validate
```

## 下一步

1. 根据您的具体需求修改 `tools.yaml` 配置
2. 连接到您的实际数据库
3. 在AI应用中集成MCP服务器
4. 测试和优化性能

## 支持资源

- [官方文档](https://googleapis.github.io/genai-toolbox/)
- [GitHub仓库](https://github.com/googleapis/genai-toolbox)
- [Discord社区](https://discord.gg/GQrFB3Ec3W)
