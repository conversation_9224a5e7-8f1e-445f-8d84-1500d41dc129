# GenAI Toolbox 启动脚本
# 使用方法: .\start-toolbox.ps1

Write-Host "正在启动 GenAI Toolbox MCP 服务器..." -ForegroundColor Green

# 设置工具箱可执行文件路径
$toolboxPath = "C:\Users\<USER>\go\bin\genai-toolbox.exe"

# 检查可执行文件是否存在
if (-not (Test-Path $toolboxPath)) {
    Write-Host "错误: 找不到 genai-toolbox.exe 文件" -ForegroundColor Red
    Write-Host "请确保已正确安装 GenAI Toolbox" -ForegroundColor Red
    exit 1
}

# 检查配置文件是否存在
if (-not (Test-Path "tools.yaml")) {
    Write-Host "错误: 找不到 tools.yaml 配置文件" -ForegroundColor Red
    Write-Host "请确保在包含 tools.yaml 的目录中运行此脚本" -ForegroundColor Red
    exit 1
}

Write-Host "配置文件: tools.yaml" -ForegroundColor Yellow
Write-Host "服务器地址: http://localhost:5000" -ForegroundColor Yellow
Write-Host "按 Ctrl+C 停止服务器" -ForegroundColor Yellow
Write-Host ""

# 启动服务器
try {
    & $toolboxPath --tools-file "tools.yaml"
}
catch {
    Write-Host "启动服务器时出错: $_" -ForegroundColor Red
    exit 1
}
