# GenAI Toolbox 测试脚本
# 用于验证安装和基本功能

Write-Host "=== GenAI Toolbox 安装验证 ===" -ForegroundColor Cyan

# 1. 检查可执行文件
$toolboxPath = "C:\Users\<USER>\go\bin\genai-toolbox.exe"
Write-Host "1. 检查可执行文件..." -ForegroundColor Yellow

if (Test-Path $toolboxPath) {
    Write-Host "   ✅ 可执行文件存在: $toolboxPath" -ForegroundColor Green
    
    # 检查版本
    try {
        $version = & $toolboxPath --version 2>&1
        Write-Host "   ✅ 版本信息: $version" -ForegroundColor Green
    }
    catch {
        Write-Host "   ❌ 无法获取版本信息: $_" -ForegroundColor Red
    }
} else {
    Write-Host "   ❌ 可执行文件不存在" -ForegroundColor Red
    exit 1
}

# 2. 检查配置文件
Write-Host "`n2. 检查配置文件..." -ForegroundColor Yellow

if (Test-Path "tools.yaml") {
    Write-Host "   ✅ 配置文件存在: tools.yaml" -ForegroundColor Green
} else {
    Write-Host "   ❌ 配置文件不存在" -ForegroundColor Red
    exit 1
}

# 3. 测试配置文件语法（如果有验证命令的话）
Write-Host "`n3. 显示帮助信息..." -ForegroundColor Yellow
try {
    & $toolboxPath --help | Select-Object -First 10
    Write-Host "   ✅ 帮助信息显示正常" -ForegroundColor Green
}
catch {
    Write-Host "   ❌ 无法显示帮助信息: $_" -ForegroundColor Red
}

Write-Host "`n=== 安装验证完成 ===" -ForegroundColor Cyan
Write-Host "如果所有检查都通过，您可以使用以下命令启动服务器：" -ForegroundColor Green
Write-Host "C:\Users\<USER>\go\bin\genai-toolbox.exe --tools-file tools.yaml" -ForegroundColor White
Write-Host "`n服务器将在 http://localhost:5000 上运行" -ForegroundColor Yellow
