# GenAI Toolbox 配置文件
# 这个文件定义了数据源、工具和工具集

# 数据源配置
sources:
  # SQLite 数据库示例（适合测试和演示）
  demo-sqlite:
    kind: sqlite
    database: "./demo.db"
    
  # PostgreSQL 数据库示例（如果您有PostgreSQL服务器）
  # my-postgres:
  #   kind: postgres
  #   host: localhost
  #   port: 5432
  #   database: mydb
  #   user: myuser
  #   password: mypassword
  
  # MySQL 数据库示例
  # my-mysql:
  #   kind: mysql
  #   host: localhost
  #   port: 3306
  #   database: mydb
  #   user: myuser
  #   password: mypassword

# 工具定义
tools:
  # SQLite 查询工具
  list-tables:
    kind: sqlite-sql
    source: demo-sqlite
    description: "列出数据库中的所有表"
    statement: "SELECT name FROM sqlite_master WHERE type='table';"
    
  create-users-table:
    kind: sqlite-sql
    source: demo-sqlite
    description: "创建用户表"
    statement: |
      CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        email TEXT UNIQUE NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );
      
  insert-user:
    kind: sqlite-sql
    source: demo-sqlite
    description: "插入新用户"
    parameters:
      - name: name
        type: string
        description: "用户姓名"
      - name: email
        type: string
        description: "用户邮箱"
    statement: "INSERT INTO users (name, email) VALUES ($1, $2);"
    
  get-users:
    kind: sqlite-sql
    source: demo-sqlite
    description: "获取所有用户"
    statement: "SELECT * FROM users ORDER BY created_at DESC;"
    
  search-users:
    kind: sqlite-sql
    source: demo-sqlite
    description: "根据姓名搜索用户"
    parameters:
      - name: name
        type: string
        description: "要搜索的用户姓名"
    statement: "SELECT * FROM users WHERE name LIKE '%' || $1 || '%';"

# 工具集定义
toolsets:
  # 基础数据库操作工具集
  basic-db:
    - list-tables
    - create-users-table
    - get-users
    
  # 用户管理工具集
  user-management:
    - create-users-table
    - insert-user
    - get-users
    - search-users
    
  # 所有工具
  all-tools:
    - list-tables
    - create-users-table
    - insert-user
    - get-users
    - search-users
